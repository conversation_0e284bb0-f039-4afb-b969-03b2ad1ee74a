{"root": ["../../src/main.ts", "../../src/router/index.ts", "../../src/services/api.ts", "../../src/services/authservice.ts", "../../src/services/customerservice.ts", "../../src/services/departmentservice.ts", "../../src/services/departmenttaskservice.ts", "../../src/services/index.ts", "../../src/services/notificationservice.ts", "../../src/services/projectservice.ts", "../../src/services/taskservice.ts", "../../src/services/ticketservice.ts", "../../src/services/userservice.ts", "../../src/stores/authstore.ts", "../../src/stores/customerstore.ts", "../../src/stores/departmentstore.ts", "../../src/stores/departmenttaskstore.ts", "../../src/stores/index.ts", "../../src/stores/notificationstore.ts", "../../src/stores/projectstore.ts", "../../src/stores/taskstore.ts", "../../src/stores/ticketstore.ts", "../../src/stores/userstore.ts", "../../src/types/imports.d.ts", "../../src/types/vite-env.d.ts", "../../src/app.vue", "../../src/components/customername.vue", "../../src/components/projectdetailsmodal.vue", "../../src/components/projectname.vue", "../../src/components/sidebar.vue", "../../src/components/ticketdetailsmodal.vue", "../../src/components/username.vue", "../../src/components/forms/customerform.vue", "../../src/components/forms/customerselectexample.vue", "../../src/components/forms/departmentform.vue", "../../src/components/forms/formulaform.vue", "../../src/components/forms/projectform.vue", "../../src/components/forms/searchableselect.vue", "../../src/components/forms/searchableselectdemo.vue", "../../src/components/forms/taskform.vue", "../../src/components/forms/ticketform.vue", "../../src/components/forms/userform.vue", "../../src/views/customers.vue", "../../src/views/dashboard.vue", "../../src/views/departments.vue", "../../src/views/login.vue", "../../src/views/messages.vue", "../../src/views/projects.vue", "../../src/views/tasks.vue", "../../src/views/tickets.vue", "../../src/views/users.vue"], "errors": true, "version": "5.7.3"}