<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useNotificationStore } from '../stores/notificationStore';
import UserName from '../components/UserName.vue';

const notificationStore = useNotificationStore();
const mynotification = computed(() => notificationStore.notifications);
const selectedNote = ref(null);
const limit = ref(10);
const skip = ref(0);
const total = computed(() => notificationStore.total);

const fetchNotifications = async () => {
  await notificationStore.fetchMyNotifications({
    $skip: skip.value,
    $limit: limit.value,
  });
};

const openMessage = (note) => {
  selectedNote.value = note;
  if (!note.isRead) markAsRead(note._id);
};

const markAsRead = async (id) => {
  await notificationStore.markAsRead(id);
  await fetchNotifications();
};

// Pagination methods
const goToPage = (page: number | string) => {
  if (typeof page === 'number') {
    skip.value = (page - 1) * limit.value;
    fetchNotifications();
  }
};

const nextPage = () => {
  if (skip.value + limit.value < total.value) {
    skip.value += limit.value;
    fetchNotifications();
  }
};

const prevPage = () => {
  if (skip.value - limit.value >= 0) {
    skip.value -= limit.value;
    fetchNotifications();
  }
};

// Computed properties for pagination
const currentPage = computed(() => Math.floor(skip.value / limit.value) + 1);
const totalPages = computed(() => Math.ceil(total.value / limit.value));
const pages = computed(() => {
  const pagesArray: (number | string)[] = [];
  const maxVisiblePages = 5;

  if (totalPages.value <= maxVisiblePages) {
    // Show all pages if there are fewer than maxVisiblePages
    for (let i = 1; i <= totalPages.value; i++) {
      pagesArray.push(i);
    }
  } else {
    // Always show first page
    pagesArray.push(1);

    // Calculate start and end of visible pages
    let start = Math.max(2, currentPage.value - 1);
    let end = Math.min(totalPages.value - 1, start + maxVisiblePages - 3);

    // Adjust start if end is too close to totalPages
    start = Math.max(2, end - (maxVisiblePages - 3));

    // Add ellipsis if needed
    if (start > 2) {
      pagesArray.push('...');
    }

    // Add middle pages
    for (let i = start; i <= end; i++) {
      pagesArray.push(i);
    }

    // Add ellipsis if needed
    if (end < totalPages.value - 1) {
      pagesArray.push('...');
    }

    // Always show last page
    pagesArray.push(totalPages.value);
  }

  return pagesArray;
});

onMounted(async () => {
  fetchNotifications();
});
</script>

<template>
  <div class="p-4 md:p-6 lg:p-8">
    <h1 class="text-xl md:text-2xl font-bold mb-4 md:mb-6">Messages</h1>

    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="grid grid-cols-1 md:grid-cols-3">
        <!-- Contacts sidebar -->
        <div class="border-r border-gray-200">
          <!-- Search -->
          <div class="p-4 border-b border-gray-200">
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                </svg>
              </div>
              <input type="text" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="Search contacts...">
            </div>
          </div>
          
          <ul class="divide-y divide-gray-200 overflow-y-auto max-h-[calc(100vh-16rem)]">
            <li
              v-for="note in mynotification"
              :key="note._id"
              class="p-4 hover:bg-blue-50 cursor-pointer" :class="{ 'bg-blue-50': selectedNote === note }"
              @click="openMessage(note)"
            >
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0 hidden xl:block">
                  <img class="h-10 w-10 rounded-full" src="/src/assets/avatar.jpg" alt="User avatar" />
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    <UserName :id="note.sender" />
                  </p>
                  <p class="text-sm text-gray-600 truncate">
                    {{ note.message }}
                  </p>
                  <p class="text-xs text-gray-400">
                    {{ new Date(note.createdAt).toLocaleString() }}
                  </p>
                </div>
                <div class="flex-shrink-0 flex flex-col items-end">
                  <span v-if="!note.isRead" class="inline-flex items-center justify-center h-5 w-5 rounded-full bg-blue-600 text-xs font-medium text-white">•</span>
                </div>
              </div>
            </li>
          </ul>

          <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
              <!-- Mobile pagination controls -->
              <div class="flex w-full justify-between sm:hidden">
                <button
                  @click="prevPage"
                  :disabled="skip === 0"
                  :class="[
                    'relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white',
                    skip === 0 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                  ]"
                >
                  Previous
                </button>
                <span class="text-sm text-gray-700">
                  Page {{ currentPage }} of {{ totalPages }}
                </span>
                <button
                  @click="nextPage"
                  :disabled="skip + limit >= total"
                  :class="[
                    'relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white',
                    skip + limit >= total ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                  ]"
                >
                  Next
                </button>
              </div>

              <!-- Desktop pagination info and controls -->
              <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p class="text-sm text-gray-700">
                    Showing
                    <span class="font-medium">{{ mynotification.length > 0 ? skip + 1 : 0 }}</span>
                    to
                    <span class="font-medium">{{ Math.min(skip + mynotification.length, total) }}</span>
                    of
                    <span class="font-medium">{{ total }}</span>
                    Notifications
                  </p>
                </div>
                <div v-if="total > 0">
                  <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      @click="prevPage"
                      :disabled="skip === 0"
                      :class="[
                        'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium',
                        skip === 0 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                      ]"
                    >
                      <span class="sr-only">Previous</span>
                      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                    </button>

                    <!-- Page numbers -->
                    <template v-for="(page, index) in pages" :key="index">
                      <!-- Ellipsis -->
                      <span v-if="page === '...'" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                        ...
                      </span>

                      <!-- Page number -->
                      <button
                        v-else
                        @click="goToPage(page)"
                        :class="[
                          'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                          currentPage === page
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        ]"
                      >
                        {{ page }}
                      </button>
                    </template>

                    <button
                      @click="nextPage"
                      :disabled="skip + limit >= total"
                      :class="[
                        'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium',
                        skip + limit >= total ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                      ]"
                    >
                      <span class="sr-only">Next</span>
                      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                      </svg>
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Chat area -->
        <div v-if="selectedNote" class="col-span-2">
          <div class="p-4 shadow">
            <h2 class="text-lg font-medium text-gray-900">
              <UserName :id="selectedNote.sender" />
            </h2>
          </div>
          <div class="p-4">
            <p class="bg-gray-50 p-4 rounded text-gray-800 text-md mt-2 whitespace-normal">{{ selectedNote.message }}</p>
            <p class="text-xs text-gray-400 mt-1">Sent on: {{ new Date(selectedNote.createdAt).toLocaleString() }}</p>
          </div>
        </div>
        <div v-else class="p-4 text-gray-400 text-sm">Select a message to view its content.</div>
      </div>
    </div>
  </div>
</template>
