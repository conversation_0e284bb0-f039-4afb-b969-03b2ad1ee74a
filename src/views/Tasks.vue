<script setup lang="ts">
// Tasks component with department and project selection
import { ref, computed, onMounted } from 'vue';
import { useDepartmentStore } from '../stores/departmentStore';
import { useProjectStore } from '../stores/projectStore';
import { useDepartmentTaskStore } from '../stores/departmentTaskStore';
import CustomerName from '../components/CustomerName.vue';

const departmentStore = useDepartmentStore();
const projectStore = useProjectStore();
const departmentTaskStore = useDepartmentTaskStore();
const selectedDepartment = ref('');
const selectedProject = ref(null);

const loadingDepartments = ref(false);
const loadingProjects = ref(false);
const loadingDepartmentTasks = ref(false);

const departments = computed(() => departmentStore.departments);
const projects = computed(() => projectStore.projects);
const departmentTasks = computed(() => departmentTaskStore.departmentTasks[0]);

const resetProjectSelection = () => {
  selectedProject.value = null;
};

const fetchDepartments = async () => {
  loadingDepartments.value = true;
  try {
    await departmentStore.fetchDepartments();
  } catch (error) {
    console.error('Error loading departments:', error);
  } finally {
    loadingDepartments.value = false;
  }
};

const fetchProjects = async () => {
  loadingProjects.value = true;
  try {
    await projectStore.fetchProjects();
  } catch (error) {
    console.error('Error loading projects:', error);
  } finally {
    loadingProjects.value = false;
  }
};

const fetchDepartmentTask = async (departmentId: string, project: any) => {
  loadingDepartmentTasks.value = true;
  try {
    await departmentTaskStore.fetchDepartmentTasks({ department: departmentId, project: project._id });
  } catch (error) {
    console.error('Error loading department tasks:', error);
  } finally {
    loadingDepartmentTasks.value = false;
  }
};

const isNonEditable = (task: any) => {
  return departmentTasks.value?.editable?.includes(task._id);
};

onMounted(async () => {
  fetchDepartments();
  fetchProjects();
});


</script>

<template>
  <div class="p-4 md:p-6 lg:p-8">
    <h1 class="text-xl md:text-2xl font-bold mb-4 md:mb-6">Tasks Management</h1>

    <!-- Department and Project Selection -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Select Department and Project</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Department Dropdown -->
        <div>
          <label for="department-select" class="block text-sm font-medium text-gray-700 mb-2">
            {{ loadingDepartments ? 'Loading departments...' : 'Choose Department' }}
          </label>
          <select
            id="department-select"
            v-model="selectedDepartment"
            @change="resetProjectSelection()"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select a department...</option>
            <option
              v-for="department in departments"
              :key="department._id"
              :value="department._id"
            >
              {{ department.name }} ({{ department.scope }})
            </option>
          </select>
        </div>

        <!-- Project Dropdown -->
        <div>
          <label for="project-select" class="block text-sm font-medium text-gray-700 mb-2">
            {{ loadingProjects ? 'Loading projects...' : 'Choose Project' }}
          </label>
          <select
            @change="fetchDepartmentTask(selectedDepartment, selectedProject)"
            id="project-select"
            v-model="selectedProject"
            :disabled="!selectedDepartment"
            class="capitalize w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
          >
            <option value="null">Select a project...</option>
            <option
              v-for="project in projects"
              :key="project._id"
              :value="project"
            >
              {{ project.name }} ({{ project.stage }})
            </option>
          </select>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6 mb-6">
      {{ selectedProject }}
      <CustomerName 
    </div>

    <!-- Department Tasks Form -->
    <div v-if="selectedProject && departmentTasks" class="bg-white rounded-lg shadow p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Project Tasks</h2>
      <form class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div
            v-for="task in departmentTasks.populatedTasks"
            :key="task.name"
            :class="{ 'md:col-span-2': task.type === 'textarea' }"
          >

            <label :for="task.name" class="block text-sm font-medium text-gray-700 mb-2">
              {{ task.name }}
            </label>

            <!-- Text Input -->
            <input
              v-if="task.type === 'string'"
              :id="task.name"
              type="text"
              :value="task.value"
              :required="task.required"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :class="{ 'bg-gray-100': isNonEditable(task) }"
              :disabled="isNonEditable(task)"
            />

            <!-- Number Input -->
            <input
              v-else-if="task.type === 'number'"
              :id="task.name"
              type="number"
              :value="task.value"
              :required="task.required"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :class="{ 'bg-gray-100': isNonEditable(task) }"
              :disabled="isNonEditable(task)"
            />

            <!-- Date Input -->
            <input
              v-else-if="task.type === 'date'"
              :id="task.name"
              type="date"
              :value="task.value"
              :required="task.required"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :class="{ 'bg-gray-100': isNonEditable(task) }"
              :disabled="isNonEditable(task)"
            />

            <!-- Select Dropdown -->
            <select
              v-else-if="task.type === 'select'"
              :id="task.name"
              :required="task.required"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :class="{ 'bg-gray-100': isNonEditable(task) }"
              :disabled="isNonEditable(task)"
            >
              <option
                v-for="option in task.options"
                :key="option"
                :value="option"
                :selected="task.value === option"
              >
                {{ option }}
              </option>
            </select>

            <!-- Checkbox Group -->
            <div v-else-if="task.type === 'checkbox'" class="space-y-2">
              <div
                v-for="option in task.options"
                :key="option"
                class="flex items-center"
              >
                <input
                  :id="`${task.name}-${option}`"
                  type="checkbox"
                  :value="option"
                  :checked="Array.isArray(task.value) && task.value.includes(option)"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  :class="{ 'bg-gray-100': isNonEditable(task) }"
                  :disabled="isNonEditable(task)"
                />
                <label :for="`${task.name}-${option}`" class="ml-2 text-sm text-gray-700">
                  {{ option }}
                </label>
              </div>
            </div>

            <!-- Textarea -->
            <textarea
              v-else-if="task.type === 'textarea'"
              :id="task.name"
              rows="3"
              :value="task.value"
              :required="task.required"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :class="{ 'bg-gray-100': isNonEditable(task) }"
              :disabled="isNonEditable(task)"
            ></textarea>

            <!-- Json Object -->
            <div v-else-if="task.type === 'jsonObject'" v-for="(value, index) in task.value" :key="index" class="space-y-2 mb-1">
              <input
                :id="`${task.name}-${index}`"
                type="text"
                :value="value"
                :required="task.required"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :class="{ 'bg-gray-100': isNonEditable(task) }"
                :disabled="isNonEditable(task)"
              />
            </div>

            <!-- Json Date -->
            <div v-else-if="task.type === 'jsonDate'" v-for="(value, key) in task.value" :key="key" class="space-y-2 mb-1">
              <input
                :id="`${task.name}-${key}`"
                type="date"
                :value="value"
                :required="task.required"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :class="{ 'bg-gray-100': isNonEditable(task) }"
                :disabled="isNonEditable(task)"
              />
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button @click="resetProjectSelection()"
            type="button"
            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Save Project Tasks
          </button>
        </div>
      </form>
    </div>

    <!-- Empty state for no form -->
    <div v-else-if="selectedProject && !departmentTasks" class="bg-white rounded-lg shadow p-6">
      <div class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No tasks available</h3>
        <p class="mt-1 text-sm text-gray-500">This department doesn't have task templates configured yet.</p>
      </div>
    </div>
  </div>
</template>
