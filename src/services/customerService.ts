import { ApiService } from './api';

export interface ApiResponse<T> {
  data: T;
  total?: number;
}

export interface Customer {
  id?: number;
  _id?: string; // MongoDB ID format
  name: string;
  status?: string;
  // Add other customer properties as needed
}



export class CustomerService {
  private static readonly baseUrl = '/customers';

  /**
   * Get all customers
   * @param params Optional parameters for pagination and filtering
   */
  static async getAll(params?: { skip?: number; limit?: number; $keywords?: string; status?: string }): Promise<ApiResponse<Customer[]>> {
    return ApiService.get<ApiResponse<Customer[]>>(this.baseUrl, { params });
  }

  /**
   * Get customer by ID
   */
  static async getById(id: number | string): Promise<ApiResponse<Customer>> {
    return ApiService.get<ApiResponse<Customer>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Create a new customer
   */
  static async create(customer: Customer): Promise<ApiResponse<Customer>> {
    return ApiService.post<ApiResponse<Customer>>(this.baseUrl, customer);
  }

  /**
   * Update an existing customer
   */
  static async update(id: number, customer: Customer): Promise<ApiResponse<Customer>> {
    return ApiService.patch<ApiResponse<Customer>>(`${this.baseUrl}/${id}`, customer);
  }

  /**
   * Delete a customer
   */
  static async delete(id: number): Promise<void> {
    return ApiService.delete<void>(`${this.baseUrl}/${id}`);
  }
}

export default CustomerService;
