import { ApiService } from './api';

export interface LoginResponse {
  token: string;
  user: {
    id: number;
    name: string;
    email: string;
    role: string;
    // Add other user properties as needed
  };
}

export class AuthService {
  /**
   * Login user and get authentication token
   */
  static async login(credentials: { email: string; password: string }): Promise<LoginResponse> {
    try {
      const response = await ApiService.post<LoginResponse>('/authentication', credentials);
      
      if (!response.token) {
        throw new Error('No token received');
      }

      localStorage.setItem('token', response.token);
      localStorage.setItem('user', JSON.stringify(response.user));
      return response;
    } catch (error) {
      // Clear any existing auth data on failure
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Logout user
   */
  static logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    return !!localStorage.getItem('token');
  }

  /**
   * Get current user information
   */
  static getCurrentUser(): any {
    const userJson = localStorage.getItem('user');
    return userJson ? JSON.parse(userJson) : null;
  }
}

export default AuthService;
