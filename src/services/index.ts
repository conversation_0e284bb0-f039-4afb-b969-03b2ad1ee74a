// Common types
export interface ApiResponse<T> {
  data: T;
  total?: number;
}

export * from './api';
export * from './authService';
export { CustomerService, type Customer } from './customerService';
export { ProjectService, type Project } from './projectService';
export { TicketService } from './ticketService';
export { TaskService, type Task } from './taskService';
export { DepartmentService, type Department } from './departmentService';
export { DepartmentTaskService, type DepartmentTask, type TaskField } from './departmentTaskService';
export { UserService, type User } from './userService';
export { NotificationService, type Notification } from './notificationService';
export { FormulaService, type Formula, type FormulaVariable } from './formulaService';

// Default exports
import AuthService from './authService';
import CustomerService from './customerService';
import ProjectService from './projectService';
import TicketService from './ticketService';
import TaskService from './taskService';
import DepartmentService from './departmentService';
import DepartmentTaskService from './departmentTaskService';
import UserService from './userService';
import NotificationService from './notificationService';
import FormulaService from './formulaService';

export {
  AuthService,
  CustomerService,
  ProjectService,
  TicketService,
  TaskService,
  DepartmentService,
  DepartmentTaskService,
  UserService,
  NotificationService,
  FormulaService
};
