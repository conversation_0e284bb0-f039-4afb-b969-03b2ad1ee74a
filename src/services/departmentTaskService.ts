import { ApiService } from './api';

export interface DepartmentTask {
  id?: number;
  department: number;
  task: number;
  assignedBy?: number;
  assignedAt?: string;
  status?: string;
  // Add other department task properties as needed
}

export class DepartmentTaskService {
  private static readonly baseUrl = '/departmenttasks/populated';

  /**
   * Get all department tasks
   */
  static async getAll(params?: { $skip?: number; $limit?: number; $keywords?: string; department?: string; project?: string }): Promise<DepartmentTask[]> {
    return ApiService.get<DepartmentTask[]>(this.baseUrl, { params });
  }

  /**
   * Get department task by ID
   */
  static async getById(id: number): Promise<DepartmentTask> {
    return ApiService.get<DepartmentTask>(`${this.baseUrl}/${id}`);
  }

  /**
   * Get tasks by department ID
   */
  static async getByDepartment(departmentId: number): Promise<DepartmentTask[]> {
    return ApiService.get<DepartmentTask[]>(`${this.baseUrl}/department/${departmentId}`);
  }

  /**
   * Create a new department task
   */
  static async create(departmentTask: DepartmentTask): Promise<DepartmentTask> {
    return ApiService.post<DepartmentTask>(this.baseUrl, departmentTask);
  }

  /**
   * Update an existing department task
   */
  static async update(id: number, departmentTask: DepartmentTask): Promise<DepartmentTask> {
    return ApiService.patch<DepartmentTask>(`${this.baseUrl}/${id}`, departmentTask);
  }

  /**
   * Delete a department task
   */
  static async delete(id: number): Promise<void> {
    return ApiService.delete<void>(`${this.baseUrl}/${id}`);
  }
}

export default DepartmentTaskService;
