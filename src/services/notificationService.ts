import { ApiService } from './api';

export interface Notification {
  id?: number;
  _id?: string;
  isRead: boolean;
  message?: string;
  readDate?: string;
  recipients?: Array<string>;
  relatedData?: string;
  relatedModel?: string;
  sender?: string;
  type?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface ApiResponse<T> {
  data: T;
  total?: number;
}

export class NotificationService {
  private static readonly baseUrl = '/notifications';
  private static readonly baseUrl2 = '/mynotifications';

  /**
   * Get all notifications
   * @param params Optional parameters for pagination and filtering
   */
  static async getAll(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string; stage?: string }): Promise<ApiResponse<Notification[]>> {
    return ApiService.get<ApiResponse<Notification[]>>(this.baseUrl, { params });
  }

  /**
   * Get notification by ID
   */
  static async getById(id: number | string): Promise<Notification> {
    return ApiService.get<Notification>(`${this.baseUrl}/${id}`);
  }

  /**
   * Create a new notification
   */
  static async create(notification: Notification): Promise<Notification> {
    return ApiService.post<Notification>(this.baseUrl, notification);
  }

  /**
   * Update an existing notification
   */
  static async update(id: number | string, notification: Notification): Promise<Notification> {
    return ApiService.patch<Notification>(`${this.baseUrl}/${id}`, notification);
  }

  /**
   * Delete a notification
   */
  static async delete(id: number | string): Promise<void> {
    return ApiService.delete<void>(`${this.baseUrl}/${id}`);
  }

  /**
   * Get all my notifications
   * @param params Optional parameters for pagination and filtering
   */
  static async getMyNotifications(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string; stage?: string }): Promise<ApiResponse<Notification[]>> {
    return ApiService.get<ApiResponse<Notification[]>>(this.baseUrl2, { params });
  }
}

export default NotificationService;
