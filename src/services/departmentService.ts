import { ApiService } from './api';

export interface Department {
  id?: number;
  _id?: string; // MongoDB ID format
  name: string;
  code?: string;
  description?: string;
  manager?: string;
  parentDepartment?: string;
  status?: string;
  budget?: string;
  location?: string;
  email?: string;
  phone?: string;
  scope?: string; // Add scope property
  createdAt?: string;
  updatedAt?: string;
  // Add other department properties as needed
}

export interface ApiResponse<T> {
  data: T;
  total?: number;
}

export class DepartmentService {
  private static readonly baseUrl = '/departments';

  /**
   * Get all departments
   */
  static async getAll(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string; stage?: string }): Promise<ApiResponse<Department[]>> {
    return ApiService.get<ApiResponse<Department[]>>(this.baseUrl, { params });
  }

  /**
   * Get department by ID
   */
  static async getById(id: number): Promise<Department> {
    return ApiService.get<Department>(`${this.baseUrl}/${id}`);
  }

  /**
   * Create a new department
   */
  static async create(department: Department): Promise<Department> {
    return ApiService.post<Department>(this.baseUrl, department);
  }

  /**
   * Update an existing department
   */
  static async update(id: number, department: Department): Promise<Department> {
    return ApiService.patch<Department>(`${this.baseUrl}/${id}`, department);
  }

  /**
   * Delete a department
   */
  static async delete(id: number): Promise<void> {
    return ApiService.delete<void>(`${this.baseUrl}/${id}`);
  }
}

export default DepartmentService;
