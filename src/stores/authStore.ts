import { defineStore } from 'pinia';
import { AuthService } from '../services';
import md5 from 'md5';

// Define the LoginCredentials interface directly in this file
interface LoginCredentials {
  email: string;
  password: string;
}

interface AuthState {
  token: string | null;
  user: any | null;
  loading: boolean;
  error: string | null;
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    token: localStorage.getItem('token'),
    user: JSON.parse(localStorage.getItem('user') || 'null'),
    loading: false,
    error: null,
  }),

  getters: {
    isAuthenticated: (state) => !!state.token,
    currentUser: (state) => state.user,
  },

  actions: {
    async login(credentials: LoginCredentials) {
      this.loading = true;
      this.error = null;
      // Clear previous token when attempting new login
      this.token = null;
      localStorage.removeItem('token');
      credentials.password = md5(credentials.password);

      try {
        const response = await AuthService.login(credentials);
        this.token = response.token;
        this.user = response.user;
        return response;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Login failed';
        // Ensure token is cleared on failure
        this.token = null;
        localStorage.removeItem('token');
        throw error;
      } finally {
        this.loading = false;
      }
    },

    logout() {
      AuthService.logout();
      this.token = null;
      this.user = null;
    },
  },
});
